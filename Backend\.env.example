# Database Configuration
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=pabbo@123
DB_NAME=LawFort
DB_POOL_SIZE=5

# Flask Configuration
SECRET_KEY=your_secret_key_here

# Email Configuration (for email notifications)
# Gmail SMTP settings (recommended for testing)
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=aaAA123/
EMAIL_USE_TLS=True

# Note: For Gmail, you need to:
# 1. Enable 2-factor authentication
# 2. Generate an "App Password" for this application
# 3. Use the app password instead of your regular password

# Alternative SMTP providers:
# Outlook: smtp-mail.outlook.com:587
# Yahoo: smtp.mail.yahoo.com:587
# Custom SMTP: your_smtp_server:port
