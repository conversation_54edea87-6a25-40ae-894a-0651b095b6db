
import React from 'react';
import { cn } from '@/lib/utils';

const Footer = () => {
  return (
    <footer className="bg-courtroom-dark text-white py-16">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          <div>
            <h3 className="text-xl font-bold mb-4">Legal Portal</h3>
            <p className="text-courtroom-neutral mb-4">
              Your comprehensive resource hub for legal education, community, career opportunities, and professional events.
            </p>
            <div className="flex space-x-4">
              <a href="#" className="text-white hover:text-golden-300 transition-colors">
                <span className="sr-only">Twitter</span>
                <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
                </svg>
              </a>
              <a href="#" className="text-white hover:text-golden-300 transition-colors">
                <span className="sr-only">LinkedIn</span>
                <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z" />
                </svg>
              </a>
              <a href="#" className="text-white hover:text-golden-300 transition-colors">
                <span className="sr-only">Facebook</span>
                <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                  <path fillRule="evenodd" d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" clipRule="evenodd" />
                </svg>
              </a>
            </div>
          </div>
          
          <div>
            <h3 className="text-lg font-bold mb-4">Quick Links</h3>
            <ul className="space-y-2">
              <li><a href="#resources" className="hover:text-golden-300 transition-colors">Resources</a></li>
              <li><a href="#community" className="hover:text-golden-300 transition-colors">Community</a></li>
              <li><a href="#career" className="hover:text-golden-300 transition-colors">Career</a></li>
              <li><a href="#events" className="hover:text-golden-300 transition-colors">Events</a></li>
              <li><a href="#" className="hover:text-golden-300 transition-colors">About Us</a></li>
              <li><a href="#" className="hover:text-golden-300 transition-colors">Contact</a></li>
            </ul>
          </div>
          
          <div>
            <h3 className="text-lg font-bold mb-4">Resources</h3>
            <ul className="space-y-2">
              <li><a href="#" className="hover:text-golden-300 transition-colors">Legal Templates</a></li>
              <li><a href="#" className="hover:text-golden-300 transition-colors">Case Law Database</a></li>
              <li><a href="#" className="hover:text-golden-300 transition-colors">Practice Areas</a></li>
              <li><a href="#" className="hover:text-golden-300 transition-colors">Legal Research</a></li>
              <li><a href="#" className="hover:text-golden-300 transition-colors">Legal News</a></li>
            </ul>
          </div>
          
          <div>
            <h3 className="text-lg font-bold mb-4">Subscribe</h3>
            <p className="text-courtroom-neutral mb-4">
              Join our newsletter to receive updates on resources, events, and opportunities.
            </p>
            <form className="space-y-2">
              <div className="flex">
                <input 
                  type="email" 
                  placeholder="Your email address" 
                  className="px-4 py-2 w-full text-black rounded-l-md focus:outline-none focus:ring-1 focus:ring-golden-500"
                />
                <button 
                  type="submit"
                  className="bg-golden-500 hover:bg-golden-600 text-white px-4 py-2 rounded-r-md transition-colors"
                >
                  Subscribe
                </button>
              </div>
              <p className="text-xs text-courtroom-neutral">
                We care about your data. Read our <a href="#" className="text-golden-300 hover:underline">Privacy Policy</a>.
              </p>
            </form>
          </div>
        </div>
        
        <div className="border-t border-courtroom-neutral/20 mt-12 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-courtroom-neutral text-sm">
              &copy; {new Date().getFullYear()} Legal Portal. All rights reserved.
            </p>
            <div className="flex space-x-6 mt-4 md:mt-0">
              <a href="#" className="text-sm text-courtroom-neutral hover:text-golden-300 transition-colors">Privacy Policy</a>
              <a href="#" className="text-sm text-courtroom-neutral hover:text-golden-300 transition-colors">Terms of Service</a>
              <a href="#" className="text-sm text-courtroom-neutral hover:text-golden-300 transition-colors">Cookie Policy</a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
