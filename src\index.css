
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 220 20% 97%;
    --foreground: 222 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222 84% 4.9%;

    --primary: 40 100% 45%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 40 100% 45%;
    --primary-foreground: 222 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  /* Hide scrollbar for all elements */
  html {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
  }

  /* Hide scrollbar for Chrome, Safari and Opera */
  html::-webkit-scrollbar {
    display: none;
  }

  /* Hide scrollbar for Chrome, Safari and Opera on specific elements */
  *::-webkit-scrollbar {
    width: 0;
    height: 0;
    background: transparent;
  }

  body {
    @apply bg-background text-foreground;
    overflow-x: hidden;
  }
}

.parallax {
  will-change: transform;
  transform-style: preserve-3d;
}

.parallax-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  transform: translateZ(-10px) scale(2);
  z-index: -1;
}

.parallax-mid {
  transform: translateZ(-5px) scale(1.5);
}

.parallax-front {
  transform: translateZ(0) scale(1);
}

.section-marker {
  position: absolute;
  left: 0;
  width: 4px;
  background-color: #e6b000;
  transition: height 0.3s ease;
}

.hover-scale {
  @apply transition-transform duration-200 hover:scale-105;
}

.story-link {
  @apply relative inline-block after:content-[''] after:absolute after:w-full after:scale-x-0 after:h-0.5 after:bottom-0 after:left-0 after:bg-primary after:origin-bottom-right after:transition-transform after:duration-300 hover:after:scale-x-100 hover:after:origin-bottom-left;
}
